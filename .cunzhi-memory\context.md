# 项目上下文信息
这是一个net4.8的openrpa项目

- 项目品牌重命名：OpenRPA→SXRPA，OpenFlow→SXRPA Console，OCR默认语言改为chi_sim(中文简体)
- 项目不再需要 open3270、vb5250、sap、sapbridge 模块，已完全删除相关目录和所有引用
- 已完成删除 open3270, vb5250, sap, sapbridge 模块的所有工作：1) 删除了目录和文件 2) 修改了解决方案文件 3) 更新了项目引用 4) 修改了WiX安装配置 5) 更新了相关代码以移除对已删除模块的引用 6) 在TerminalEmulator中添加了适当的错误消息
- wix toolset 3.14 安装目录 ：C:\Program Files (x86)\WiX Toolset v3.14
- 项目.NET版本分析：当前混合使用net462(4个项目)、net48(22个项目)、net46(1个)、net40(1个)。计划统一升级到.NET Framework 4.8以保持Win7兼容性，这是支持Win7的最高版本。4.8.1不支持Win7。
- 已完成工具箱组件汉化和移除工作：1) 完善了工具箱组件汉化，将硬编码的中文替换为资源文件引用，新增了OpenRPA.Script、OpenRPA.IE、OpenRPA.Image、OpenRPA.Java、OpenRPA.NM、OpenRPA.Office、OpenRPA.FileWatcher等组件的中文资源条目；2) 完全移除了OpenRPA.MSSpeech(语音)和OpenRPA.Elis.Rossum组件，包括：删除解决方案文件中的项目引用和配置、删除WiX安装项目中的组件定义和文件引用、删除项目目录和所有相关文件、从工具箱代码中移除组件分类处理、从资源文件中移除相关条目
- MSI安装包构建问题已解决：修改build_installer.ps1使用x64平台，在WiX项目文件中添加PythonSource变量定义
- 已将所有MSI安装包模块设置为默认选中安装，包括JavaFeature、ScriptFeature、AviRecorderFeature、RDServicePluginFeature
- MSI安装包构建成功，程序能正常启动，但缺少一些资源文件（主要是.resources.dll和XmlSerializers.dll文件）。需要对比dist/net48目录和安装目录，补充缺失的文件到WiX配置中。
- 浏览器扩展本地安装功能已完成：1)修复了Chrome Manifest V3兼容性问题(window对象引用)；2)实现了扩展包自动构建和多目标分发(运行时目录、MSI内容目录、Extensions目录)；3)添加了智能扩展包查找机制，支持多路径查找和容错；4)创建了build_extensions.bat自动化构建脚本；5)扩展包已准备好集成到MSI安装包中，用户安装后可直接本地安装浏览器插件，无需访问在线商店。
- WebSocket库Win7兼容性问题已修复：1) 在OpenRPA.Net项目中添加了System.Net.WebSockets.Client.Managed NuGet包引用；2) 恢复了WebSocketClient.cs中的版本检测代码，Win8+使用原生ClientWebSocket，Win7使用托管实现System.Net.WebSockets.Managed.ClientWebSocket；3) 确保项目在Win7和更高版本Windows系统上都能正常使用WebSocket功能
- 修复了气球提示组件运行时窗口闪烁和通知不显示的问题：1) 修改了Window_StateChanged逻辑，确保窗口隐藏到通知栏时图标保持可见；2) 解决了Python.NET在Windows Server 2008离线环境的兼容性问题；3) 实现了正确的窗口行为：最小化收至任务栏，关闭收至通知栏，通知栏支持右键菜单
