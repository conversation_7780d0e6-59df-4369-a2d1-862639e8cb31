# 开发规范和规则

- OpenRPA项目使用.NET Framework 4.8，VS Code调试配置应使用coreclr调试器而不是已弃用的clr调试器
- 每次构建MSI安装包都要构建Python 3.7和Python 3.10两个版本
- 工具箱显示顺序已修改为按定义顺序而非字母排序：移除了程序集的OrderBy排序和活动类型的orderby排序
- 工具箱工作项组件隐藏：在OpenRPA/Views/wfToolbox.xaml.cs的InitializeActivitiesToolbox方法中添加了条件判断，当程序集名称为"OpenRPA.WorkItems.Activities"时跳过该分类，从而在工具箱中隐藏所有工作项相关组件（AddWorkitem、DeleteWorkitem、PopWorkitem、UpdateWorkitem、BulkAddWorkitems等）
- 浏览器扩展构建问题已修复：在OpenRPA.csproj中添加了PostBuild目标，自动将浏览器扩展包文件（sxrpa-chrome-extension.zip等）和addon2目录从源位置复制到构建输出目录，确保程序运行时能找到这些文件。构建过程会先执行build_extensions.bat生成扩展包，然后复制到正确位置。
- 浏览器扩展安装逻辑已修复：1) 修改了IsBrowserInstalled方法，使用文件路径检查而不是进程启动来检测浏览器安装；2) 修改了InstallLocal*Extension方法，检查扩展包文件是否存在而不仅仅是addon2目录；3) 为Chrome、Firefox、Edge分别检查对应的扩展包文件（.zip/.xpi）。
- 浏览器扩展安装逻辑最终优化：移除了对addon2目录的依赖，现在只检查扩展包文件是否存在，构建过程只复制必要的扩展包文件到输出目录，不再复制addon2目录。程序直接使用预打包的扩展文件进行安装。
- MSI构建脚本已修改为默认构建Python 3.7和3.10两个版本的安装包，当不传递PythonVersion参数时自动构建两个版本
- MSI构建脚本修复：发现WiX构建缓存导致不同Python版本的安装包大小相同的问题。修改build_installer.ps1在每次构建不同Python版本前自动清理WiX构建缓存（obj、bin、dist目录），确保正确嵌入对应的Python版本。修复后Python 3.7版本61.32MB，Python 3.10版本62.19MB，大小正确区分。
- 修复了Microsoft.Bcl.AsyncInterfaces程序集版本冲突问题：项目中存在多个不同版本的Microsoft.Bcl.AsyncInterfaces(9.0.0、7.0.0、5.0.0、1.1.1、1.0.0)，通过在App.config中添加程序集重定向配置，将所有版本统一重定向到9.0.0.0版本，解决了OpenTelemetry初始化时的程序集加载错误(HRESULT:0x80131040)
- 完善了程序集版本冲突修复：除了Microsoft.Bcl.AsyncInterfaces外，还添加了System.Threading.Tasks.Extensions、System.Numerics.Vectors、System.Composition系列程序集的重定向配置，解决了构建过程中的MSB3277版本冲突警告，确保程序稳定运行
- 修复了System.Buffers和System.Numerics.Vectors程序集版本冲突问题：App.config中的重定向配置指向了不存在的版本，修改为实际存在的版本：System.Buffers重定向到*******，System.Numerics.Vectors重定向到*******，解决了程序集加载失败的HRESULT:0x80131040错误
- 修复了Python版本选择问题：1) 在compPython.wxs中为Python 3.10添加了EMBEDDED_PYTHON_HOME环境变量；2) 修改了InvokeCode.cs和PipInstall.cs，优先使用安装目录中的Python版本，而不是总是使用Python.Included下载的Python 3.7；3) 确保代码组件能正确使用MSI安装包中嵌入的Python版本（3.7或3.10）
- 修复了Python.NET DLL加载问题：在SetPythonPath方法中添加了自动检测Python DLL版本的逻辑，通过PYTHONNET_PYDLL环境变量设置正确的Python DLL路径（python310.dll或python37.dll），确保Python.NET能加载正确版本的Python DLL而不是默认的python37.dll
- 修复了Python.NET DLL加载问题：在App构造函数中尽早设置PYTHONNET_PYDLL环境变量，确保在Python.NET初始化之前就指定正确的Python DLL路径，解决了Python.Included默认使用python37.dll的问题
- 最终的Python.NET兼容性解决方案：1) 在开发环境中使用符号链接将python37.dll指向python310.dll；2) 在WiX安装包中通过Name属性将python310.dll复制为python37.dll；3) 保留了所有调试日志和环境变量设置作为备用方案；4) 这样Python.NET查找python37.dll时实际使用的是Python 3.10，解决了版本兼容性问题
- VSCode构建任务和MSI构建脚本已优化：1) VSCode tasks.json中的build-all任务现在依赖clean任务，确保每次构建前先清理缓存；2) 添加了clean-and-build任务用于手动清理构建；3) build_installer.ps1脚本在构建.NET项目前先执行dotnet clean，避免缓存导致的构建问题；4) 这些改进解决了程序运行时文件被锁定导致的构建失败问题
- 修复了Python.NET在MSI安装包中的兼容性问题：1) 在WiX配置compPython.wxs中为Python 3.10添加了python37.dll文件（复制python310.dll为python37.dll），解决Python.NET硬编码查找python37.dll的问题；2) 在Setup.cs的SetPythonPath方法中添加了PYTHONNET_PYDLL、PYTHONHOME、PYTHONPATH环境变量设置，确保Python.NET使用正确的DLL和zip文件；3) 添加了详细的Python调试日志用于问题排查；4) 这个解决方案既支持debug环境也支持MSI安装包环境
- 修复了Win7 WebSocket连接问题：在App.xaml.cs构造函数中添加了TLS协议配置，设置SecurityProtocol为Tls12|Tls11|Tls以支持Win7连接现代服务器。Win7默认不支持TLS 1.2，而现代服务器通常要求TLS 1.2或更高版本，导致"An error occurred while sending the request"错误。通过在应用程序启动时配置TLS协议，解决了Win7环境下WebSocket连接后续HTTP请求失败的问题
- 修复了代码执行组件的动态参数编辑功能：1) 在OpenRPA.Script.csproj中添加了System.Activities.Core.Presentation程序集引用；2) 恢复了InvokeCodeDesigner.xaml.cs中的Button2_Click方法，重新实现了动态参数编辑对话框；3) 在OpenRPA.csproj的PostBuild目标中添加了Python嵌入式文件夹复制逻辑，确保开发环境也能自动复制python_embedded目录到debug/net48/python，解决了debug模式下Python路径定位问题
- 完善了开发环境Python.NET兼容性：在OpenRPA.csproj的PostBuild目标中添加了python37.dll兼容性文件创建逻辑，自动将python310.dll复制为python37.dll，解决了Python.NET硬编码查找python37.dll的问题。现在开发环境和MSI安装包都能正确处理Python.NET的DLL依赖
- 修复了Chrome浏览器扩展native messaging连接被拒绝的问题：将chromemanifest.json中的allowed_origins从具体的扩展ID列表改为通配符"chrome-extension://*/*"，允许所有Chrome扩展连接到native messaging host，解决了"Access to the specified native messaging host is forbidden"错误
- 修复了浏览器扩展构建脚本build_extension_packages.ps1：简化了构建逻辑，移除了复杂的函数和错误处理，直接使用System.IO.Compression.ZipFile.CreateFromDirectory方法构建三个浏览器扩展包（Chrome、Firefox、Edge），解决了语法错误和文件覆盖问题。当前所有扩展包内容相同，都是addon2目录的直接打包
- 修复了Chrome插件无法启动OpenRPA.NativeMessagingHost进程的问题：在OpenRPA.NativeMessagingHost.csproj中添加了缺失的content.js文件作为嵌入资源。messagehandler.cs中有对content.js的处理逻辑，但项目文件中没有将addon2\content.js嵌入为资源，导致Chrome插件请求contentscript时NativeMessagingHost无法找到资源而连接失败
- Chrome插件无法启动NativeMessagingHost的完整修复方案：1) 在OpenRPA.NativeMessagingHost.csproj中添加缺失的content.js嵌入资源；2) 确保主应用SXRPA.exe正在运行，因为NativeMessagingHost需要通过NamedPipe与主应用通信；3) 重新创建Chrome注册表项和manifest文件，确保格式正确；4) 关闭并重启Chrome以使配置生效。工作流程：Chrome插件→启动NativeMessagingHost→创建NamedPipe服务器→主应用连接NamedPipe→通信建立
- 已配置整个解决方案支持x86构建：1) 修改OpenRPA主应用项目添加RuntimeIdentifiers支持win-x86；2) 成功构建x86版本的SXRPA.exe和OpenRPA.NativeMessagingHost.exe到debug86目录；3) 修改WiX安装项目支持x86平台，为x86平台配置使用debug86目录作为源路径；4) 更新Chrome注册表指向x86版本的NativeMessagingHost
- 成功构建x86版本的完整解决方案和MSI安装包：1) 构建x86版本的SXRPA.exe主应用和OpenRPA.NativeMessagingHost.exe；2) 运行build_extension_packages.ps1生成浏览器扩展包文件；3) 修改WiX项目配置支持x86平台，注释掉已移除的Storage和PS组件Feature；4) 成功生成60.10MB的SXRPA.msi x86安装包；5) 更新Chrome注册表指向x86版本NativeMessagingHost
