name: "Close stale issues"
on:
  push:
  pull_request:
  schedule:
    - cron: "0 0 * * *"  
jobs:
  stale:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/stale@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: "This issue has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions."
          stale-pr-message: "This PR has been automatically marked as stale because it has not had recent activity. It will be closed if no further activity occurs. Thank you for your contributions."
          days-before-stale: 3
          days-before-close: 2
          stale-issue-label: "stale"
          exempt-issue-labels: "pinned,security,bug,enhancement,pending,awaiting-approval,work-in-progress"
          stale-pr-label: "stale"
          exempt-pr-labels: "pinned,security,bug,enhancement,pending,awaiting-approval,work-in-progress"
          debug-only: false
