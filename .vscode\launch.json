{"version": "0.2.0", "configurations": [{"name": "启动 SXRPA", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/debug/net48/SXRPA.exe", "args": [], "cwd": "${workspaceFolder}/debug/net48", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build"}, {"name": "启动 SXRPA (清理缓存)", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/debug/net48/SXRPA.exe", "args": ["--debug"], "cwd": "${workspaceFolder}/debug/net48", "stopAtEntry": true, "console": "internalConsole", "preLaunchTask": "build-all"}, {"name": "启动 JavaBridge", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/debug/net48/OpenRPA.JavaBridge.exe", "args": [], "cwd": "${workspaceFolder}/debug/net48", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-javabridge"}, {"name": "启动 NativeMessagingHost", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/debug/net48/OpenRPA.NativeMessagingHost.exe", "args": [], "cwd": "${workspaceFolder}/debug/net48", "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "build-nativemessaging"}, {"name": "附加到进程", "type": "coreclr", "request": "attach", "processId": "${command:pickProcess}"}]}