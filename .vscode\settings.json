{"dotnet.defaultSolution": "OpenRPA.sln", "files.exclude": {"**/bin": true, "**/obj": true, "**/.vs": true, "**/debug": false, "**/debug86": false, "**/dist": true}, "search.exclude": {"**/bin": true, "**/obj": true, "**/debug": true, "**/debug86": true, "**/dist": true, "**/node_modules": true}, "files.associations": {"*.xaml": "xml", "*.config": "xml", "*.resx": "xml", "*.wxs": "xml", "*.wxi": "xml"}, "xml.fileAssociations": [{"pattern": "**/*.xaml", "systemId": "http://schemas.microsoft.com/winfx/2006/xaml/presentation"}], "omnisharp.enableEditorConfigSupport": true, "omnisharp.enableRoslynAnalyzers": true, "csharp.semanticHighlighting.enabled": true, "editor.formatOnSave": false, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "dotnet.completion.showCompletionItemsFromUnimportedNamespaces": true, "dotnet.inlayHints.enableInlayHintsForParameters": true, "dotnet.inlayHints.enableInlayHintsForLiteralParameters": true, "dotnet.inlayHints.enableInlayHintsForIndexerParameters": true, "dotnet.inlayHints.enableInlayHintsForObjectCreationParameters": true, "dotnet.inlayHints.enableInlayHintsForOtherParameters": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatDifferOnlyBySuffix": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchMethodIntent": true, "dotnet.inlayHints.suppressInlayHintsForParametersThatMatchArgumentName": true}