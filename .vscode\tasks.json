{"version": "2.0.0", "tasks": [{"label": "build", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/OpenRPA.sln", "--configuration", "Debug", "--verbosity", "minimal", "/property:GenerateFullPaths=true"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "build-javabridge", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/OpenRPA.JavaBridge/OpenRPA.JavaBridge.csproj", "--configuration", "Debug", "--verbosity", "minimal", "/property:GenerateFullPaths=true"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "build-nativemessaging", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/OpenRPA.NativeMessagingHost/OpenRPA.NativeMessagingHost.csproj", "--configuration", "Debug", "--verbosity", "minimal", "/property:GenerateFullPaths=true"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "clean", "command": "dotnet", "type": "process", "args": ["clean", "${workspaceFolder}/OpenRPA.sln", "--configuration", "Debug"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "clean-all-thorough", "command": "powershell", "type": "process", "args": ["-ExecutionPolicy", "Bypass", "-File", "${workspaceFolder}/clean_all.ps1", "-Force"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": []}, {"label": "clean-and-build", "dependsOrder": "sequence", "dependsOn": ["clean", "build-all-no-clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "build-all", "dependsOrder": "sequence", "dependsOn": ["clean-all-thorough", "build-all-no-clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "build-all-no-clean", "command": "dotnet", "type": "process", "args": ["build", "${workspaceFolder}/OpenRPA.sln", "--configuration", "Debug", "--verbosity", "minimal", "/property:GenerateFullPaths=true"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "restore", "command": "dotnet", "type": "process", "args": ["restore", "${workspaceFolder}/OpenRPA.sln"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": "$msCompile"}, {"label": "build-debug-fresh", "dependsOrder": "sequence", "dependsOn": ["clean-all-thorough", "restore", "build-all-no-clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "shared", "showReuseMessage": true, "clear": true}}]}