language: csharp
mono: none
os: linux
dotnet: 3.1.101

jobs:
  include:
    - os: linux
      dist: bionic
    - os: osx

env:
  global:
    - DOTNET_SKIP_FIRST_TIME_EXPERIENCE: true
    - DOTNET_CLI_TELEMETRY_OPTOUT: 1

script:
  - dotnet restore ./LiteDB/LiteDB.csproj
  - dotnet build ./LiteDB/LiteDB.csproj -f netstandard2.0
  - dotnet test ./LiteDB.Tests/LiteDB.Tests.csproj --logger:console --verbosity=minimal

notifications:
  email: false  
