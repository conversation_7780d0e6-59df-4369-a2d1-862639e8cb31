﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <AssemblyName>LiteDB.Tests</AssemblyName>
    <RootNamespace>LiteDB.Tests</RootNamespace>
    <Authors><PERSON><PERSON><PERSON><PERSON></Authors>
    <Copyright>MIT</Copyright>
    <NeutralLanguage>en-US</NeutralLanguage>
    <GenerateDocumentationFile>false</GenerateDocumentationFile>
    <NoWarn>1701;1702;1705;1591;0618</NoWarn>
  </PropertyGroup>

  <ItemGroup Condition="'$(Configuration)' == 'Release'">
    <Compile Remove="Internals\**" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Utils\Json\person.json" />
    <None Remove="Utils\Json\zip.json" />
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Utils\Json\person.json" />
    <EmbeddedResource Include="Utils\Json\zip.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="FluentAssertions" Version="5.10.2" />
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="16.2.0" />
    <PackageReference Include="xunit" Version="2.4.1" />
    <PackageReference Include="xunit.runner.console" Version="2.4.1" />
    <PackageReference Include="xunit.runner.reporters" Version="2.4.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LiteDB\LiteDB.csproj" />
  </ItemGroup>

</Project>