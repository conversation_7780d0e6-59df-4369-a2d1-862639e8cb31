﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFrameworks>net45;netstandard1.3;netstandard2.0</TargetFrameworks>
    <AssemblyVersion>5.0.11</AssemblyVersion>
    <FileVersion>5.0.11</FileVersion>
    <VersionPrefix>5.0.11</VersionPrefix>
    <Authors><PERSON><PERSON><PERSON><PERSON></Authors>
    <Product>LiteDB</Product>
    <Description>LiteDB - A lightweight embedded .NET NoSQL document store in a single datafile</Description>
    <Copyright>MIT</Copyright>
    <NeutralLanguage>en-US</NeutralLanguage>
    <Title>LiteDB</Title>
    <PackageId>LiteDB</PackageId>
    <PackageVersion>5.0.11</PackageVersion>
    <PackageTags>database nosql embedded</PackageTags>
    <PackageIcon>icon_64x64.png</PackageIcon>
    <PackageLicenseFile>LICENSE</PackageLicenseFile>
    <PackageProjectUrl>https://www.litedb.org</PackageProjectUrl>
    <RepositoryUrl>https://github.com/mbdavid/LiteDB</RepositoryUrl>
    <RepositoryType>git</RepositoryType>
    <RootNamespace>LiteDB</RootNamespace>
    <AssemblyName>LiteDB</AssemblyName>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <NetStandardImplicitPackageVersion Condition=" '$(TargetFramework)' == 'netstandard1.3' ">1.6.1</NetStandardImplicitPackageVersion>
    <NoWarn>1701;1702;1705;1591;0618</NoWarn>
    <DocumentationFile>bin\$(Configuration)\$(TargetFramework)\LiteDB.xml</DocumentationFile>
    <SignAssembly Condition="'$(OS)'=='Windows_NT'">true</SignAssembly>
    <AssemblyOriginatorKeyFile Condition="'$(Configuration)' == 'Release'">LiteDB.snk</AssemblyOriginatorKeyFile>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
  </PropertyGroup>
  
  <!--
  == All variables ==
  HAVE_APP_DOMAIN
  HAVE_PROCESS
  HAVE_ENVIRONMENT
  HAVE_SHA1_MANAGED
  -->

  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|AnyCPU'">
    <DefineConstants>TRACE;DEBUG</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' == 'net45'">
    <DefineConstants>HAVE_SHA1_MANAGED;HAVE_APP_DOMAIN;HAVE_PROCESS;HAVE_ENVIRONMENT</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(TargetFramework)' == 'netstandard2.0'">
    <DefineConstants>HAVE_SHA1_MANAGED</DefineConstants>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|net45|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|netstandard1.3|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)|$(TargetFramework)|$(Platform)'=='Release|netstandard2.0|AnyCPU'">
    <Optimize>False</Optimize>
  </PropertyGroup>

  <!-- Begin References -->
  <ItemGroup>
    <None Include="..\LICENSE" Pack="true" PackagePath="" />
    <None Include="..\icon_64x64.png" Pack="true" PackagePath="\" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'net45'">
    <Reference Include="System" />
    <Reference Include="System.Runtime" />
  </ItemGroup>

  <ItemGroup Condition="'$(TargetFramework)' == 'netstandard1.3'">
    <PackageReference Include="System.Reflection.TypeExtensions" Version="4.5.1" />
    <PackageReference Include="System.Security.Cryptography.Algorithms" Version="4.3.1" />
  </ItemGroup>
  
  <!-- End References -->

</Project>
