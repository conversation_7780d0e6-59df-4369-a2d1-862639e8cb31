version: 5.0.{build}
branches:
  only:
  - master
image: Visual Studio 2019
configuration: 
  - Debug
  - Release
before_build:
  - cmd: nuget restore LiteDB.sln
build:
  project: LiteDB.sln
  publish_nuget: true
  verbosity: minimal
for:
-
  matrix:
    only:
      - configuration: Release
  artifacts:
    - path: LiteDB\bin\Release\LiteDB*.nupkg
  deploy:
  - provider: Webhook
    url: https://app.signpath.io/API/v1/f5b329b8-705f-4d6c-928a-19465b83716b/Integrations/AppVeyor?ProjectKey=LiteDB.git&SigningPolicyKey=release-signing
    authorization:
      secure: 3eLjGkpQC1wg1s5GIEqs7yk/V8OZNnpKmpwdsaloGExc5jMspM4nA7u/UlG5ugraEyXRC05ZxLU4FIfH2V2BEg==
