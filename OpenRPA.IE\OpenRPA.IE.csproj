﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <TargetFramework>net48</TargetFramework>
    <UseWPF>true</UseWPF>
    <EnableDefaultPageItems>false</EnableDefaultPageItems>
    <GeneratePackageOnBuild>false</GeneratePackageOnBuild>
    <Authors><PERSON></Authors>
    <Product>OpenRPA</Product>
    <Description>Activities and record plugin for handling Internet Explorer, created for OpenRPA robot</Description>
    <PackageLicenseExpression>MPL-2.0</PackageLicenseExpression>
    <PackageProjectUrl>https://github.com/open-rpa/openrpa</PackageProjectUrl>
    <Version>1.0.32</Version>
    <PackageReleaseNotes>1.0.32 Add custom timeout for openining browser
1.0.31 Fix launch IE error
1.0.30 Fix endless loop bug
1.0.29 Fix select value for IE, add SupportSelect
1.0.28 Make caching of IE object optional, and disable by default
1.0.27 Add support for nested getelement, when using xpath
1.0.26 Fix bug when using open selector -&gt; select
1.0.25 Add WaitForReady to GetElement
1.0.24 Add experimental support for XPath for IE, also doing recording / Improve OpenURL for IE
1.0.23 Change target framework to 4.7.2
1.0.22 Add support for variables in selector
1.0.21 Add langauges
1.0.20 Fix IE google Search Snippet
1.0.18 update interfaces reference
    

1.0.17 fix double click
1.0.15 update interfaces reference
    

1.0.14 support for seperate images
      
1.0.13 update interfaces reference
</PackageReleaseNotes>
    <PackageIcon>ie.png</PackageIcon>
    <Configurations>Debug;Release;ReleaseNuget;PrepInstaller</Configurations>
    <PackageTags>OpenRPA</PackageTags>
    <RepositoryUrl>https://github.com/open-rpa/openrpa</RepositoryUrl>
    <Platforms>AnyCPU;x86</Platforms>
    <Company>OpenIAP</Company>
    <!-- 限制资源文件只生成简体中文和英文 -->
    <SatelliteResourceLanguages>en;zh</SatelliteResourceLanguages>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|AnyCPU'">
    <OutputPath>..\debug</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x86'">
    <OutputPath>..\debug86</OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|AnyCPU'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x86'">
    <OutputPath>..\dist</OutputPath>
    <Optimize>False</Optimize>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|AnyCPU'">
    <OutputPath></OutputPath>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='PrepInstaller|x86'">
    <OutputPath />
  </PropertyGroup>
  <Target Name="SetNuspecProperties" BeforeTargets="GenerateNuspec">
    <PropertyGroup>
      <NuspecProperties>$(NuspecProperties);id=$(AssemblyName)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);config=$(Configuration)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);version=$(PackageVersion)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);description=$(Description)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);author=$(Authors)</NuspecProperties>
      <NuspecProperties>$(NuspecProperties);configurationname=$(ConfigurationName)</NuspecProperties>
    </PropertyGroup>
  </Target>
  <ItemGroup>
    <None Include="Resources\ie.png" Pack="true" PackagePath="\" />
    <None Remove="Activities\GetElementDesigner.xaml" />
    <None Remove="Activities\OpenURLDesigner.xaml" />
    <None Remove="javascriptxml.js" />
    <None Remove="Resources\designer\gethtmlelement.png" />
    <None Remove="Resources\designer\openurl.png" />
    <None Remove="Resources\search.png" />
    <None Remove="Resources\searchfailed.png" />
    <None Remove="Resources\searchfound.png" />
    <None Remove="Resources\toolbox\gethtmlelement.png" />
    <None Remove="Resources\toolbox\openurl.png" />
    <None Remove="Snippets\GoogleSearch.xaml" />
    <None Remove="Views\RecordPluginView.xaml" />
  </ItemGroup>
  <ItemGroup>
    <Resource Include="Resources\designer\openurl.png" />
    <Resource Include="Resources\designer\gethtmlelement.png" />
    <Resource Include="Snippets\GoogleSearch.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Resource>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="javascriptxml.js" />
    <EmbeddedResource Include="Resources\searchfound.png" />
    <EmbeddedResource Include="Resources\search.png" />
    <EmbeddedResource Include="Resources\toolbox\openurl.png" />
    <EmbeddedResource Include="Resources\toolbox\gethtmlelement.png" />
    <EmbeddedResource Include="Resources\searchfailed.png" />
  </ItemGroup>
  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.40" />
  </ItemGroup>
  <ItemGroup>
    <Reference Include="Interop.MSHTML">
      <HintPath>lib\Interop.MSHTML.dll</HintPath>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </Reference>
    <Reference Include="Interop.SHDocVw">
      <HintPath>lib\Interop.SHDocVw.dll</HintPath>
      <EmbedInteropTypes>true</EmbedInteropTypes>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="PresentationCore" />
    <Reference Include="PresentationFramework" />
    <Reference Include="System.Activities" />
    <Reference Include="System.Activities.Presentation" />
    <Reference Include="System.Xaml" />
    <Reference Include="UIAutomationClient" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Page Include="Activities\GetElementDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Activities\OpenURLDesigner.xaml">
      <Generator>MSBuild:Compile</Generator>
    </Page>
    <Page Include="Views\RecordPluginView.xaml">
      <SubType>Designer</SubType>
      <Generator>MSBuild:Compile</Generator>
    </Page>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\OpenRPA.Interfaces\OpenRPA.Interfaces.csproj" />
  </ItemGroup>
  <ItemGroup>
    <Compile Update="Resources\strings.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>strings.resx</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Update="Resources\strings.ru.resx">
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\strings.ro.resx">
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
      <Generator>PublicResXFileCodeGenerator</Generator>
    </EmbeddedResource>
    <EmbeddedResource Update="Resources\strings.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <LastGenOutput>strings.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
  <ItemGroup>
    <None Update="lib\Interop.SHDocVw.dll">
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </None>
  </ItemGroup>
  <Target Name="PushNugetPackage" AfterTargets="Pack" Condition="'$(Configuration)' == 'ReleaseNuget'">
    <Exec Command="nuget.exe push $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg -Source nuget.org" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'PrepInstaller'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <Target Name="PostBuild" AfterTargets="Pack" Condition="'$(Configuration)' == 'Debug'">
    <Exec Command="copy $(OutputPath)..\$(PackageId).$(PackageVersion).nupkg C:\code\OpenRPA\packages" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <UserProperties BuildVersion_StartDate="2000/1/1" />
    </VisualStudio>
  </ProjectExtensions>
</Project>