﻿<sap:ActivityDesigner x:Class="OpenRPA.Activities.ForEachDataRowDesigner"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sa="clr-namespace:System.Activities;assembly=System.Activities"
    xmlns:sap="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sapv="clr-namespace:System.Activities.Presentation.View;assembly=System.Activities.Presentation"
    xmlns:or="clr-namespace:OpenRPA.Resources"
    xmlns:s="clr-namespace:System.Data;assembly=System.Data"
    xmlns:Converters="clr-namespace:System.Activities.Presentation.Converters;assembly=System.Activities.Presentation"
    >


    <sap:ActivityDesigner.Icon>
        <DrawingBrush>
            <DrawingBrush.Drawing>
                <ImageDrawing>
                    <ImageDrawing.Rect>
                        <Rect Location="0,0" Size="16,16" ></Rect>
                    </ImageDrawing.Rect>
                    <ImageDrawing.ImageSource>
                        <BitmapImage UriSource="/OpenRPA;component/Resources/designer/foreach.png" ></BitmapImage>
                    </ImageDrawing.ImageSource>
                </ImageDrawing>
            </DrawingBrush.Drawing>
        </DrawingBrush>
    </sap:ActivityDesigner.Icon>


    
    <sap:ActivityDesigner.Resources>
        <Converters:ArgumentToExpressionConverter x:Key="ArgumentToExpressionConverter" />
        <DataTemplate x:Key="Collapsed">
            <Label Padding="0,0,0,0" FontStyle="Italic" Foreground="{x:Static SystemColors.GrayTextBrush}" HorizontalAlignment="Center"
                       VerticalAlignment="Center" Content="{x:Static or:strings.activity_click_to_view}"></Label>
        </DataTemplate>
        <DataTemplate x:Key="Expanded">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50" />
                    <ColumnDefinition Width="50" />
                    <ColumnDefinition Width="20" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition />
                    <RowDefinition />
                </Grid.RowDefinitions>
                <TextBlock Text="For each " Grid.Column="0" />
                <TextBox Text="{Binding ModelItem.Body.Argument.Name}" Grid.Column="1" />
                <TextBlock Text=" in " Grid.Column="2" />
                <sapv:ExpressionTextBox Expression="{Binding Path=ModelItem.DataTable, Converter={StaticResource ArgumentToExpressionConverter}}"
                                       ExpressionType="s:DataTable"
                                       OwnerActivity="{Binding ModelItem}" Grid.Column="3" />
                <sap:WorkflowItemPresenter Item="{Binding ModelItem.Body.Handler}"
                                    HintText="{x:Static or:strings.activity_drop_hint}" AllowedItemType="sa:Activity"
                                      Grid.Row="1" Grid.ColumnSpan="4" />
            </Grid>

        </DataTemplate>
        <Style x:Key="ExpandOrCollapsedStyle" TargetType="{x:Type ContentPresenter}">
            <Setter Property="ContentTemplate" Value="{DynamicResource Expanded}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Path=ShowExpanded}" Value="false">
                    <Setter Property="ContentTemplate" Value="{DynamicResource Collapsed}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </sap:ActivityDesigner.Resources>
    <Grid>
        <ContentPresenter Style="{DynamicResource ExpandOrCollapsedStyle}" Content="{Binding}" />
    </Grid>
</sap:ActivityDesigner>
