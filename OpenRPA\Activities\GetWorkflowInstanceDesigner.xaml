﻿<sap:ActivityDesigner x:Class="OpenRPA.Activities.GetWorkflowInstanceDesigner"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sa="clr-namespace:System.Activities;assembly=System.Activities"
    xmlns:sap="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sapv="clr-namespace:System.Activities.Presentation.View;assembly=System.Activities.Presentation"
    xmlns:or="clr-namespace:OpenRPA.Resources"
    xmlns:local="clr-namespace:OpenRPA"
    xmlns:s="clr-namespace:System;assembly=mscorlib"
    xmlns:Converters="clr-namespace:System.Activities.Presentation.Converters;assembly=System.Activities.Presentation"
    xmlns:i="clr-namespace:OpenRPA;assembly=OpenRPA.Interfaces"                      
    >


    <sap:ActivityDesigner.Icon>
        <DrawingBrush>
            <DrawingBrush.Drawing>
                <ImageDrawing>
                    <ImageDrawing.Rect>
                        <Rect Location="0,0" Size="16,16" ></Rect>
                    </ImageDrawing.Rect>
                    <ImageDrawing.ImageSource>
                        <BitmapImage UriSource="/OpenRPA;component/Resources/designer/getworkflowinstance.png" ></BitmapImage>
                    </ImageDrawing.ImageSource>
                </ImageDrawing>
            </DrawingBrush.Drawing>
        </DrawingBrush>
    </sap:ActivityDesigner.Icon>



    <sap:ActivityDesigner.Resources>
        <Converters:ArgumentToExpressionConverter x:Key="ArgumentToExpressionConverter" />
        <DataTemplate x:Key="Collapsed">
        </DataTemplate>
        <DataTemplate x:Key="Expanded">
            <StackPanel>

                <sapv:ExpressionTextBox HintText="Hint" MaxWidth="180" Margin="5"
                            OwnerActivity="{Binding Path=ModelItem}"
                            Expression="{Binding Path=ModelItem.Result, Mode=TwoWay, 
                    Converter={StaticResource ArgumentToExpressionConverter}, ConverterParameter=Out}"
                                        UseLocationExpression="True" 
                            ExpressionType="local:WorkflowInstance" HorizontalAlignment="Stretch" />

            </StackPanel>
        </DataTemplate>
        <Style x:Key="ExpandOrCollapsedStyle" TargetType="{x:Type ContentPresenter}">
            <Setter Property="ContentTemplate" Value="{DynamicResource Expanded}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Path=ShowExpanded}" Value="false">
                    <Setter Property="ContentTemplate" Value="{DynamicResource Collapsed}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </sap:ActivityDesigner.Resources>
    <Grid>
        <ContentPresenter Style="{DynamicResource ExpandOrCollapsedStyle}" Content="{Binding}" />
    </Grid>
</sap:ActivityDesigner>
