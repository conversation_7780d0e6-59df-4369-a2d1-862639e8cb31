﻿<sap:ActivityDesigner x:Class="OpenRPA.Activities.InvokeOpenRPADesigner"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:c="clr-namespace:OpenRPA.Interfaces;assembly=OpenRPA.Interfaces"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sa="clr-namespace:System.Activities;assembly=System.Activities"
    xmlns:sap="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sapv="clr-namespace:System.Activities.Presentation.View;assembly=System.Activities.Presentation"
    xmlns:or="clr-namespace:OpenRPA.Resources"
    Loaded="ActivityDesigner_Loaded"
    >


    <sap:ActivityDesigner.Icon>
        <DrawingBrush>
            <DrawingBrush.Drawing>
                <ImageDrawing>
                    <ImageDrawing.Rect>
                        <Rect Location="0,0" Size="16,16" ></Rect>
                    </ImageDrawing.Rect>
                    <ImageDrawing.ImageSource>
                        <BitmapImage UriSource="/OpenRPA;component/Resources/designer/invokerpaworkflow.png" ></BitmapImage>
                    </ImageDrawing.ImageSource>
                </ImageDrawing>
            </DrawingBrush.Drawing>
        </DrawingBrush>
    </sap:ActivityDesigner.Icon>

    <sap:ActivityDesigner.Resources>
        <c:InArgumentStringConverter x:Key="InArgumentStringConverter" />
        <DataTemplate x:Key="Collapsed">
            <Label Padding="0,0,0,0" FontStyle="Italic" Foreground="{x:Static SystemColors.GrayTextBrush}" HorizontalAlignment="Center"
                       VerticalAlignment="Center" Content="{x:Static or:strings.activity_click_to_view}"></Label>
        </DataTemplate>
        <DataTemplate x:Key="Expanded">
            <StackPanel>
                <ComboBox 
                ItemsSource="{Binding workflows}"
                DisplayMemberPath="ProjectAndName"
                SelectedValuePath="ProjectAndName"
                SelectedValue="{Binding Path=ModelItem.workflow, Mode=TwoWay, Converter={StaticResource InArgumentStringConverter}}"
                >
                </ComboBox>
                <!--<Button Click="Button_Click" Content="{x:Static or:strings.activity_add_variables}"></Button>-->
                <Button Click="Button2_Click" Content="{x:Static or:strings.activity_map_variables}"></Button>
            </StackPanel>
        </DataTemplate>
        <Style x:Key="ExpandOrCollapsedStyle" TargetType="{x:Type ContentPresenter}">
            <Setter Property="ContentTemplate" Value="{DynamicResource Expanded}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Path=ShowExpanded}" Value="false">
                    <Setter Property="ContentTemplate" Value="{DynamicResource Collapsed}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </sap:ActivityDesigner.Resources>
    <Grid>
        <ContentPresenter Style="{DynamicResource ExpandOrCollapsedStyle}" Content="{Binding}" />
    </Grid>
</sap:ActivityDesigner>
