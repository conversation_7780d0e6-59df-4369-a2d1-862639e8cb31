﻿<sap:ActivityDesigner x:Class="OpenRPA.WorkItems.ThrowBusinessRuleExceptionDesigner"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:sa="clr-namespace:System.Activities;assembly=System.Activities"
    xmlns:sap="clr-namespace:System.Activities.Presentation;assembly=System.Activities.Presentation"
    xmlns:sapv="clr-namespace:System.Activities.Presentation.View;assembly=System.Activities.Presentation"
    xmlns:s="clr-namespace:System;assembly=mscorlib"
    xmlns:Converters="clr-namespace:System.Activities.Presentation.Converters;assembly=System.Activities.Presentation"
    xmlns:or="clr-namespace:OpenRPA.Resources"
                      xmlns:c="clr-namespace:OpenRPA.Interfaces;assembly=OpenRPA.Interfaces"
    >


    <sap:ActivityDesigner.Icon>
        <DrawingBrush>
            <DrawingBrush.Drawing>
                <ImageDrawing>
                    <ImageDrawing.Rect>
                        <Rect Location="0,0" Size="16,16" ></Rect>
                    </ImageDrawing.Rect>
                    <ImageDrawing.ImageSource>
                        <BitmapImage UriSource="/OpenRPA;component/Resources/designer/throwbusinessruleexception.png" ></BitmapImage>
                    </ImageDrawing.ImageSource>
                </ImageDrawing>
            </DrawingBrush.Drawing>
        </DrawingBrush>
    </sap:ActivityDesigner.Icon>

    <sap:ActivityDesigner.Resources>
        <Converters:ArgumentToExpressionConverter x:Key="ArgumentToExpressionConverter" />
        <c:InArgumentStringConverter x:Key="InArgumentStringConverter" />
        <DataTemplate x:Key="Collapsed">
            <Label Padding="0,0,0,0" FontStyle="Italic" Foreground="{x:Static SystemColors.GrayTextBrush}" HorizontalAlignment="Center"
                       VerticalAlignment="Center" Content="{x:Static or:strings.activity_click_to_view}"></Label>
        </DataTemplate>
        <DataTemplate x:Key="Expanded">
            <StackPanel>
                <sapv:ExpressionTextBox HintText="Hint" MaxWidth="180" Margin="5"
                            OwnerActivity="{Binding Path=ModelItem}"
                            Expression="{Binding Path=ModelItem.Message, Mode=TwoWay, Converter={StaticResource ArgumentToExpressionConverter}, ConverterParameter=In}"
                            ExpressionType="s:String" HorizontalAlignment="Stretch" />

            </StackPanel>
        </DataTemplate>
        <Style x:Key="ExpandOrCollapsedStyle" TargetType="{x:Type ContentPresenter}">
            <Setter Property="ContentTemplate" Value="{DynamicResource Expanded}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding Path=ShowExpanded}" Value="false">
                    <Setter Property="ContentTemplate" Value="{DynamicResource Collapsed}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </sap:ActivityDesigner.Resources>
    <Grid>
        <ContentPresenter Style="{DynamicResource ExpandOrCollapsedStyle}" Content="{Binding}" />
    </Grid>
</sap:ActivityDesigner>
