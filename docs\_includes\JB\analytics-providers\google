<!-- Global site tag (gtag.js) - Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=UA-139588965-1"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', 'UA-139588965-1');
</script>
    <!-- Note: plugin scripts must be included after the tracking snippet. -->
    <script src="https://ipmeta.io/plugin.js"></script>

    <script>
        provideGtagPlugin({
            apiKey: 'e7879b2ae5246cd193d0468681902fba9589430ddcfc4e715bb90f1c8f6762bdd8da3b15a756b630',
            serviceProvider: 'dimension1',
            networkDomain: 'dimension2',
            networkType: 'dimension3',
        });
    </script>

<script type="text/javascript">
_linkedin_partner_id = "1147386";
window._linkedin_data_partner_ids = window._linkedin_data_partner_ids || [];
window._linkedin_data_partner_ids.push(_linkedin_partner_id);
</script><script type="text/javascript">
(function(){var s = document.getElementsByTagName("script")[0];
var b = document.createElement("script");
b.type = "text/javascript";b.async = true;
b.src = "https://snap.licdn.com/li.lms-analytics/insight.min.js";
s.parentNode.insertBefore(b, s);})();
</script>
<noscript>
<img height="1" width="1" style="display:none;" alt="" src="https://dc.ads.linkedin.com/collect/?pid=1147386&fmt=gif" />
</noscript>