{% comment%}<!--
The liquid_raw helper is a way to display raw liquid code, as opposed to parsing it.
Normally you'd use Liquid's built in 'raw' tag. 
The problem is GitHub Jekyll does not support the current Liquid release.
GitHub Jekyll supports the deprecated 'literal' tag.
Using one will break the other if you plan to deploy to GitHub pages.
  see: https://github.com/mojombo/jekyll/issues/425

Since I don't want to mess with Liquid versions, I'll just rewrite the way I 
intend to give liquid examples. It's not an elegant by any means:

Usage: 
  1) Define a 'text' variable with the block of liquid code you intend to display.
  2) Pass the text variable to include JB/liquid_raw

  example:
  {% capture text %}|.% for tag in tags_list %.|
    <li><a href="|.{ site.var.tags_path }.||.{ tag[0] }.|-ref">|.{ tag[0] }.| <span>|.{tag[1].size}.|</span></a></li>
  |.% endfor %.|

  |.% assign tags_list = null %.|{% endcapture %}    
  {% include JB/liquid_raw %}
  
  As seen here, you must use "|." and ".|" as opening and closing brackets.
-->{% endcomment%}

{% if site.JB.liquid_raw.provider == "custom" %}
  {% include custom/liquid_raw %}
{% else %}
  <pre><code>{{text | replace:"|.", "&#123;" | replace:".|", "&#125;" | replace:">", "&gt;" | replace:"<", "&lt;" }}</code></pre>
{% endif %}
{% assign text = nil %}