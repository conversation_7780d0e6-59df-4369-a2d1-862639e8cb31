{% capture jbcache %}
  <!--
  - Dynamically set liquid variables for working with URLs/paths
  -->
  {% if site.JB.setup.provider == "custom" %}
    {% include custom/setup %}
  {% else %}
    {% if site.safe and site.JB.BASE_PATH and site.JB.BASE_PATH != '' %}
      {% assign BASE_PATH = site.JB.BASE_PATH %}
      {% assign HOME_PATH = site.JB.BASE_PATH %}
    {% else %}
      {% assign BASE_PATH = nil %}
      {% assign HOME_PATH = "/" %}
    {% endif %}

    {% if site.JB.ASSET_PATH %}
      {% assign ASSET_PATH = site.JB.ASSET_PATH %}
    {% else %}
      {% capture ASSET_PATH %}{{ BASE_PATH }}/assets/themes/{{ layout.theme.name }}{% endcapture %}
    {% endif %}
  {% endif %}
{% endcapture %}{% assign jbcache = nil %}
