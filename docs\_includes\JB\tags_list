{% comment %}<!--
The tags_list include is a listing helper for tags.
Usage:
  1) assign the 'tags_list' variable to a valid array of tags.
  2) include JB/tags_list
  example:
    <ul>
  	  {% assign tags_list = site.tags %}  
  	  {% include JB/tags_list %}
  	</ul>
  
  Notes: 
    Tags can be either a Hash of tag objects (hashes) or an Array of tag-names (strings).
    The encapsulating 'if' statement checks whether tags_list is a Hash or Array.
    site.tags is a Hash while page.tags is an array.
    
  This helper can be seen in use at: ../_layouts/default.html
-->{% endcomment %}

{% if site.JB.tags_list.provider == "custom" %}
  {% include custom/tags_list %}
{% else %}
  {% if tags_list.first[0] == null %}
    {% for tag in tags_list %} 
    	<li><a href="{{ BASE_PATH }}{{ site.JB.tags_path }}#{{ tag }}-ref">{{ tag }} <span>{{ site.tags[tag].size }}</span></a></li>
    {% endfor %}
  {% else %}
    {% for tag in tags_list %} 
    	<li><a href="{{ BASE_PATH }}{{ site.JB.tags_path }}#{{ tag[0] }}-ref">{{ tag[0] }} <span>{{ tag[1].size }}</span></a></li>
    {% endfor %}
  {% endif %}
{% endif %}
{% assign tags_list = nil %}
